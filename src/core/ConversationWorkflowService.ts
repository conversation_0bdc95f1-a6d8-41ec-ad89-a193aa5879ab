import { DBOS } from '@dbos-inc/dbos-sdk';
import { ConversationService } from './ConversationService';
import { PromptService } from './PromptService';
import { MessageQueueService } from './MessageQueueService';

import { LLMService } from '../services/LLMService';
import { GeminiLLMService } from '../services/GeminiLLMService';
import { Conversation, Message, DelayedMessage } from '../models/types';
import { logger } from '../utils/Logger';
import { MoodService } from './MoodService';

export class ConversationWorkflowService {
    // Static LLM service instance for dependency injection
    private static llmService: LLMService | null = null;

    // Method to set LLM service for testing
    static setLLMService(service: LLMService): void {
        ConversationWorkflowService.llmService = service;
    }

    // Helper method to get the LLM service (injected or default)
    private static getLLMService(): LLMService {
        return ConversationWorkflowService.llmService || new GeminiLLMService();
    }

    @DBOS.transaction()
    static async getMessageCount(conversationId: number): Promise<number> {
        const result = await DBOS.knexClient('forachat.messages')
            .where('conversation_id', conversationId)
            .count('id as count')
            .first() as { count: string } | undefined;
        return result ? Number(result.count) : 0;
    }

    @DBOS.transaction()
    static async updateConversationMetadata(
        conversationId: number,
        theme?: string,
        skills?: string[]
    ): Promise<void> {
        const updateData: any = {};
        if (theme !== undefined) updateData.theme = theme;
        if (skills !== undefined) updateData.skills = JSON.stringify(skills);

        if (Object.keys(updateData).length > 0) {
            await DBOS.knexClient('forachat.conversations')
                .where('id', conversationId)
                .update(updateData);
        }
    }

    @DBOS.transaction()
    static async getConversationRelevanceContext(conversationId: number): Promise<{
        conversation: Conversation | null;
        recentMessages: Message[];
        messageCount: number;
    }> {
        // Get conversation metadata directly (avoid nested transaction)
        const conversation = await DBOS.knexClient<Conversation>('forachat.conversations')
            .where('id', conversationId)
            .first();

        // Parse skills JSON if it's stored as string (same logic as getConversation)
        if (conversation && conversation.skills && typeof conversation.skills === 'string') {
            conversation.skills = JSON.parse(conversation.skills as string);
        }

        // Build context from recent messages
        const recentMessages = await DBOS.knexClient<Message>('forachat.messages')
            .where('conversation_id', conversationId)
            .orderBy('id', 'desc')
            .limit(5);

        // Get message count directly (avoid nested transaction)
        const messageCountResult = await DBOS.knexClient('forachat.messages')
            .where('conversation_id', conversationId)
            .count('id as count')
            .first() as { count: string } | undefined;

        const messageCount = messageCountResult ? Number(messageCountResult.count) : 0;

        return {
            conversation: conversation || null,
            recentMessages: recentMessages.reverse(),
            messageCount
        };
    }

    @DBOS.workflow()
    static async determineConversationRelevance(
        userMessage: string,
        conversationId: number
    ): Promise<boolean> {
        // Get conversation context through transaction
        const { conversation, recentMessages, messageCount } = await ConversationWorkflowService.getConversationRelevanceContext(conversationId);

        const context = recentMessages.map(msg => `${msg.character}: ${msg.text}`).join('\n');

        // Log conversation relevance analysis context
        logger.info(`=== CONVERSATION RELEVANCE ANALYSIS ===`);
        logger.info(`Conversation ID: ${conversationId}`);
        logger.info(`Message Count in Context: ${messageCount}`);
        logger.info(`User Message: "${userMessage}"`);
        logger.info(`Recent Context (${recentMessages.length} messages): ${context || 'No previous messages'}`);
        logger.info(`Theme: ${conversation?.theme || 'Not set'}`);
        logger.info(`Skills: ${conversation?.skills ? JSON.stringify(conversation.skills) : 'Not set'}`);

        // Get system prompt
        const systemPrompt = await PromptService.getSystemPrompt();

        // Ask LLM to determine if the message is related to the conversation with detailed reasoning
        const llmService = ConversationWorkflowService.getLLMService();
        const llmResponse = await llmService.generate(
            systemPrompt,
            `Analyze if this user message is related to the ongoing conversation and provide detailed reasoning.

Conversation context:
${context || 'No previous messages in this conversation'}

${conversation?.theme ? `Conversation theme: ${conversation.theme}` : ''}
${conversation?.skills ? `Conversation skills: ${Array.isArray(conversation.skills) ? conversation.skills.join(', ') : conversation.skills}` : ''}

User message: ${userMessage}

Consider starting a new conversation if:
- The user message introduces a completely different interpersonal skill topic
- The theme would change significantly (e.g., from "conflict resolution" to "team building")
- The user is asking about skills not covered in the current conversation's skills list

Respond with a JSON object containing:
- "isRelated": boolean (true if related to the conversation, false if it's a new topic)
- "reasoning": string (detailed explanation of why you think it is or isn't related)
- "confidence": number (0-100, how confident you are in this assessment)
- "keyFactors": array of strings (key factors that influenced your decision)
- "suggestedTheme": string (what the theme should be for this message)
- "suggestedSkills": array of strings (what skills this message relates to)

Consider factors like:
- Topic continuity
- Reference to previous messages or characters
- Contextual relevance
- Natural conversation flow
- Whether it's a complete topic change
- Theme and skills alignment`
        );

        // Log the LLM's detailed analysis
        if (llmResponse && typeof llmResponse === 'object') {
            logger.info(`=== LLM CONVERSATION RELEVANCE DECISION ===`);
            // Cast to any to access custom properties that aren't in ChatResponse
            const response = llmResponse as any;
            logger.info(`Is Related: ${response.isRelated}`);
            logger.info(`Confidence: ${response.confidence || 'Not provided'}%`);
            logger.info(`Reasoning: ${response.reasoning || 'No reasoning provided'}`);
            logger.info(`Key Factors: ${response.keyFactors ? JSON.stringify(response.keyFactors) : 'Not provided'}`);
            logger.info(`Suggested Theme: ${response.suggestedTheme || 'Not provided'}`);
            logger.info(`Suggested Skills: ${response.suggestedSkills ? JSON.stringify(response.suggestedSkills) : 'Not provided'}`);

            // Extract the determination
            if ('isRelated' in response) {
                const isRelated = !!response.isRelated;

                // Additional check: if theme or skills would change significantly, consider starting new conversation
                if (isRelated && conversation?.theme && response.suggestedTheme) {
                    const themeChanged = ConversationWorkflowService.hasThemeChangedSignificantly(
                        conversation.theme,
                        response.suggestedTheme
                    );

                    const skillsChanged = ConversationWorkflowService.haveSkillsChangedSignificantly(
                        conversation.skills,
                        response.suggestedSkills
                    );

                    if (themeChanged || skillsChanged) {
                        logger.info(`Theme or skills changed significantly - starting new conversation`);
                        logger.info(`Theme changed: ${themeChanged}, Skills changed: ${skillsChanged}`);
                        return false;
                    }
                }

                logger.info(`Final Decision: ${isRelated ? 'CONTINUING existing conversation' : 'STARTING new conversation'}`);
                return isRelated;
            }
        }

        // Log if we couldn't parse the response
        logger.warn(`Could not parse LLM relevance response: ${JSON.stringify(llmResponse)}`);
        logger.info(`Final Decision: CONTINUING existing conversation (default fallback)`);

        // Default to true if we can't determine
        return true;
    }

    /**
     * Determines if the theme has changed significantly enough to warrant a new conversation
     */
    private static hasThemeChangedSignificantly(currentTheme: string, suggestedTheme: string): boolean {
        if (!currentTheme || !suggestedTheme) {
            return false;
        }

        // Normalize themes for comparison
        const normalizeTheme = (theme: string) => theme.toLowerCase().trim();
        const current = normalizeTheme(currentTheme);
        const suggested = normalizeTheme(suggestedTheme);

        // If themes are identical, no change
        if (current === suggested) {
            return false;
        }

        // Define theme categories that are similar enough to continue conversation
        const themeCategories = [
            ['communication', 'active listening', 'feedback', 'difficult conversations'],
            ['conflict resolution', 'mediation', 'negotiation', 'problem solving'],
            ['team building', 'collaboration', 'teamwork', 'group dynamics'],
            ['leadership', 'management', 'delegation', 'mentoring'],
            ['emotional intelligence', 'empathy', 'self-awareness', 'social skills'],
            ['networking', 'relationship building', 'professional relationships'],
            ['presentation', 'public speaking', 'meeting facilitation'],
            ['time management', 'productivity', 'organization', 'prioritization']
        ];

        // Check if both themes belong to the same category
        for (const category of themeCategories) {
            const currentInCategory = category.some(keyword => current.includes(keyword));
            const suggestedInCategory = category.some(keyword => suggested.includes(keyword));

            if (currentInCategory && suggestedInCategory) {
                return false; // Same category, continue conversation
            }
        }

        // If themes don't belong to the same category, it's a significant change
        return true;
    }

    /**
     * Determines if the skills have changed significantly enough to warrant a new conversation
     */
    private static haveSkillsChangedSignificantly(
        currentSkills: string[] | string | undefined,
        suggestedSkills: string[] | undefined
    ): boolean {
        if (!currentSkills || !suggestedSkills || suggestedSkills.length === 0) {
            return false;
        }

        // Normalize current skills to array
        const currentSkillsArray = Array.isArray(currentSkills)
            ? currentSkills
            : (typeof currentSkills === 'string' ? JSON.parse(currentSkills) : []);

        if (currentSkillsArray.length === 0) {
            return false;
        }

        // Normalize skills for comparison
        const normalizeSkill = (skill: string) => skill.toLowerCase().trim();
        const currentNormalized = currentSkillsArray.map(normalizeSkill);
        const suggestedNormalized = suggestedSkills.map(normalizeSkill);

        // Check for overlap - if there's any common skill, continue conversation
        const hasOverlap = currentNormalized.some((skill: string) =>
            suggestedNormalized.includes(skill)
        );

        // If there's overlap, don't start new conversation
        if (hasOverlap) {
            return false;
        }

        // If no overlap and both lists have skills, it's a significant change
        return true;
    }

    /**
     * Comprehensive method to determine if a new conversation should be started
     * based on theme and skills changes
     */
    static shouldStartNewConversation(
        currentConversation: { theme?: string; skills?: string[] | string } | null,
        newTheme?: string,
        newSkills?: string[]
    ): boolean {
        // If no current conversation, always start new
        if (!currentConversation) {
            return true;
        }

        // If no new theme or skills provided, continue current conversation
        if (!newTheme && (!newSkills || newSkills.length === 0)) {
            return false;
        }

        // Check theme changes
        const themeChanged = newTheme ?
            ConversationWorkflowService.hasThemeChangedSignificantly(
                currentConversation.theme || '',
                newTheme
            ) : false;

        // Check skills changes
        const skillsChanged = newSkills ?
            ConversationWorkflowService.haveSkillsChangedSignificantly(
                currentConversation.skills,
                newSkills
            ) : false;

        return themeChanged || skillsChanged;
    }

    /**
     * Compare two conversations to see if they have similar themes and skills
     */
    static areConversationsSimilar(
        conversation1: { theme?: string; skills?: string[] | string } | null,
        conversation2: { theme?: string; skills?: string[] | string } | null
    ): boolean {
        if (!conversation1 || !conversation2) {
            return false;
        }

        // Check if themes are similar
        const themeSimilar = conversation1.theme && conversation2.theme ?
            !ConversationWorkflowService.hasThemeChangedSignificantly(
                conversation1.theme,
                conversation2.theme
            ) : true; // If either has no theme, consider similar

        // Check if skills are similar
        const skillsSimilar = (conversation1.skills && conversation2.skills) ?
            !ConversationWorkflowService.haveSkillsChangedSignificantly(
                conversation1.skills,
                Array.isArray(conversation2.skills)
                    ? conversation2.skills
                    : (typeof conversation2.skills === 'string' ? JSON.parse(conversation2.skills) : [])
            ) : true; // If either has no skills, consider similar

        return themeSimilar && skillsSimilar;
    }

    private static async processLLMResponse(
        llmResponse: any,
        conversation: { id: number },
        userMessage: string
    ): Promise<any> {
        if (!llmResponse || !llmResponse.reply || !Array.isArray(llmResponse.reply)) {
            logger.error(`Invalid LLM response structure: ${JSON.stringify(llmResponse)}`);
            throw new Error("Invalid response from LLM - missing or invalid reply array");
        }

        // Handle case where LLM determines request is not related to interpersonal skills
        if (llmResponse.reply.length === 0) {
            logger.info(`LLM determined request is not related to interpersonal skills. Theme: ${llmResponse.theme}`);

            // Don't show warning for general greetings
            if (llmResponse.theme !== "general greeting") {
                // Create a helpful response explaining this
                const helpfulResponse = {
                    character: "Fora",
                    text: "Hey! 👋 I noticed your message might not be about interpersonal workplace skills. I'm here to help with things like communication, teamwork, conflict resolution, and other people skills at work. Could you rephrase your question to focus on the interpersonal aspect? 😊",
                    delay: 1000
                };

                llmResponse.reply = [helpfulResponse];
                llmResponse.theme = llmResponse.theme || "Request clarification needed";
                llmResponse.skills = llmResponse.skills || [];
            } else {
                // For general greetings, pick one character at random and prompt immediately
                const allCharacters = ['Fora', 'Jan', 'Lou'];
                const randomCharacter = allCharacters[Math.floor(Math.random() * allCharacters.length)];

                logger.info(`General greeting detected - randomly selected ${randomCharacter} to respond immediately`);

                // Generate immediate response from the selected character
                const greetingResponse = await ConversationWorkflowService.generateImmediateGreetingResponse(
                    randomCharacter,
                    userMessage,
                    conversation
                );

                if (greetingResponse) {
                    llmResponse.reply = [greetingResponse];
                } else {
                    // Fallback to empty reply if generation fails
                    llmResponse.reply = [];
                }

                llmResponse.skills = llmResponse.skills || [];
                // Suppress theme from being sent to client for general greetings
                llmResponse.theme = "";
            }
        }

        // Store all response messages with duplicate checking
        for (const responseMessage of llmResponse.reply) {
            const messageExists = await ConversationService.checkMessageExists(
                responseMessage.character,
                responseMessage.text,
                conversation.id
            );

            if (!messageExists) {
                await ConversationService.addMessage(responseMessage.character, responseMessage.text, conversation.id);
                logger.info(`Added response message from ${responseMessage.character}: ${responseMessage.text.substring(0, 50)}...`);
            } else {
                logger.info(`Skipped duplicate response from ${responseMessage.character}: ${responseMessage.text.substring(0, 50)}...`);
            }
        }

        // Store theme and skills if provided in the LLM response
        if (llmResponse.theme || llmResponse.skills) {
            await ConversationWorkflowService.updateConversationMetadata(
                conversation.id,
                llmResponse.theme,
                llmResponse.skills
            );
        }

        return llmResponse;
    }

    /**
     * Generate an immediate greeting response from a specific character
     */
    private static async generateImmediateGreetingResponse(
        character: string,
        userMessage: string,
        conversation: { id: number }
    ): Promise<{ character: string; text: string; delay: number } | null> {
        try {
            // Use character-specific system prompt
            const characterPromptName = `${character.toLowerCase()}_system`;
            let systemPrompt = await PromptService.getSystemPrompt(characterPromptName);

            // Add mood context to the system prompt if available
            const conversationData = await ConversationService.getConversation(conversation.id);
            if (conversationData?.character_moods) {
                const { MoodService } = await import('./MoodService');
                const moods = MoodService.parseCharacterMoods(conversationData.character_moods);
                if (moods) {
                    const moodContext = MoodService.formatCharacterMoodForPrompt(character, moods);
                    if (moodContext) {
                        systemPrompt = `${systemPrompt}\n\n${moodContext}`;
                    }
                }
            }

            // Create a simple greeting prompt
            const greetingPrompt = `The user just said: "${userMessage}"

This appears to be a general greeting. Respond naturally and warmly as ${character} would, keeping it brief and welcoming. This is the start of a conversation about interpersonal workplace skills.

Respond with a JSON object in this format:
{
  "reply": [
    {
      "character": "${character}",
      "text": "your response here",
      "delay": 1000
    }
  ]
}`;

            const llmService = ConversationWorkflowService.getLLMService();
            const llmResponse = await llmService.generate(systemPrompt, greetingPrompt);

            if (llmResponse && llmResponse.reply && llmResponse.reply.length > 0) {
                const response = llmResponse.reply[0];
                logger.info(`Generated immediate greeting response from ${character}: ${response.text.substring(0, 50)}...`);
                return {
                    character: response.character || character,
                    text: response.text,
                    delay: 1000 // Immediate response
                };
            }

            return null;
        } catch (error) {
            logger.error(`Failed to generate immediate greeting response from ${character}:`, error);
            return null;
        }
    }

    static getCharacterThoughtInfo(
        conversation: { id: number },
        userMessage: string,
        llmResponse: any
    ): Array<{character: string, context: string, delay: number}> {
        // For general greetings, don't generate character thoughts since we already have an immediate response
        if (llmResponse.theme === "general greeting") {
            logger.info(`Skipping character thoughts for general greeting - immediate response already generated`);
            return [];
        }

        // Prepare character thought information for the calling workflow to queue
        const allCharacters = ['Fora', 'Jan', 'Lou'];
        const conversationContext = `User: ${userMessage}\n` +
            llmResponse.reply.map((msg: DelayedMessage) => `${msg.character}: ${msg.text}`).join('\n');

        const characterThoughts: Array<{character: string, context: string, delay: number}> = [];

        // Calculate delays for all characters
        for (const character of allCharacters) {
            // Use longer delays (30-60 seconds) for non-greeting themes
            const baseDelay = 30000;
            const randomDelay = Math.random() * 30000;
            const initialDelay = baseDelay + randomDelay;

            characterThoughts.push({
                character,
                context: conversationContext,
                delay: initialDelay
            });
        }

        logger.info(`Generated ${characterThoughts.length} character thoughts for theme: ${llmResponse.theme}, conversation: ${conversation.id}`);
        logger.info(`Character thought delays: ${characterThoughts.map(t => `${t.character}:${Math.round(t.delay)}ms`).join(', ')}`);

        return characterThoughts;
    }

    @DBOS.workflow()
    static async chatWorkflow(userMessage: string, conversationId?: number): Promise<any> {
        let conversation;
        if (conversationId) {
            // Use existing conversation
            conversation = { id: conversationId };
        } else {
            // Create new conversation
            conversation = await ConversationService.createConversation();
        }

        await ConversationService.addMessage("user", userMessage, conversation.id);

        // Get message count for logging context
        const messageCount = await ConversationWorkflowService.getMessageCount(conversation.id);

        const systemPrompt = await PromptService.getSystemPrompt();
        const llmService = ConversationWorkflowService.getLLMService();

        // Build comprehensive context including theme, skills, and conversation history
        let finalPrompt = userMessage;
        if (conversationId) {
            // Get conversation metadata and full message history
            const conversationData = await ConversationService.getConversation(conversationId);
            const allMessages = await ConversationService.getConversationMessages(conversationId);

            // Build full conversation history (excluding the just-added user message)
            const conversationHistory = allMessages
                .slice(0, -1) // Exclude the current user message
                .map(msg => `${msg.character}: ${msg.text}`)
                .join('\n');

            // Build context with theme, skills, moods, and conversation history
            let contextParts = [];

            if (conversationData?.theme) {
                contextParts.push(`Conversation theme: ${conversationData.theme}`);
            }

            if (conversationData?.skills) {
                const skillsArray = Array.isArray(conversationData.skills)
                    ? conversationData.skills
                    : (typeof conversationData.skills === 'string' ? JSON.parse(conversationData.skills) : []);
                if (skillsArray.length > 0) {
                    contextParts.push(`Conversation skills: ${skillsArray.join(', ')}`);
                }
            }

            // Add character moods to context
            if (conversationData?.character_moods) {
                const moods = MoodService.parseCharacterMoods(conversationData.character_moods);
                if (moods) {
                    contextParts.push(MoodService.formatMoodsForPrompt(moods));
                }
            }

            if (conversationHistory.length > 0) {
                contextParts.push(`Full conversation history:\n${conversationHistory}`);
            }

            if (contextParts.length > 0) {
                finalPrompt = `${contextParts.join('\n\n')}\n\nCurrent user message: ${userMessage}`;
            }
        }

        const llmResponse = await llmService.generate(systemPrompt, finalPrompt);

        // Log parsed LLM response with context
        logger.info(`=== LLM RESPONSE ===`);
        logger.info(`Conversation: ${conversation.id}, Message Count: ${messageCount}, Responder: default agent`);
        logger.info(`Parsed Response: ${JSON.stringify(llmResponse)}`);

        // Store original theme before processing (for character thoughts check)
        const originalTheme = llmResponse.theme;

        // Process the LLM response
        const processedResponse = await ConversationWorkflowService.processLLMResponse(llmResponse, conversation, userMessage);

        // Get character thought information for the calling workflow to queue
        // Use original theme to determine if character thoughts should be generated
        const characterThoughts = ConversationWorkflowService.getCharacterThoughtInfo(conversation, userMessage, { ...processedResponse, theme: originalTheme });

        return { ...processedResponse, conversationId: conversation.id, characterThoughts };
    }

    @DBOS.workflow()
    static async interruptedChatWorkflow(
        userMessage: string,
        previousMessages: Array<{character: string, text: string}>,
        conversationId?: number
    ): Promise<any> {
        let conversation;
        if (conversationId) {
            conversation = { id: conversationId };
        } else {
            conversation = await ConversationService.createConversation();
        }

        // Build comprehensive context including theme, skills, and conversation history
        let fullPrompt = `User (interrupting): ${userMessage}`;

        if (conversationId) {
            // Get conversation metadata and full message history for context
            const conversationData = await ConversationService.getConversation(conversationId);
            const allMessages = await ConversationService.getConversationMessages(conversationId);

            // Build full conversation history (excluding the current user message that was just added)
            const conversationHistory = allMessages
                .slice(0, -1) // Exclude the current user message
                .map(msg => `${msg.character}: ${msg.text}`)
                .join('\n');

            // Build context with theme, skills, moods, and conversation history
            let contextParts = [];

            if (conversationData?.theme) {
                contextParts.push(`Conversation theme: ${conversationData.theme}`);
            }

            if (conversationData?.skills) {
                const skillsArray = Array.isArray(conversationData.skills)
                    ? conversationData.skills
                    : (typeof conversationData.skills === 'string' ? JSON.parse(conversationData.skills) : []);
                if (skillsArray.length > 0) {
                    contextParts.push(`Conversation skills: ${skillsArray.join(', ')}`);
                }
            }

            // Add character moods to context
            if (conversationData?.character_moods) {
                const moods = MoodService.parseCharacterMoods(conversationData.character_moods);
                if (moods) {
                    contextParts.push(MoodService.formatMoodsForPrompt(moods));
                }
            }

            if (conversationHistory.length > 0) {
                contextParts.push(`Full conversation history:\n${conversationHistory}`);
            }

            // Include the previous messages that were being processed when interrupted
            if (previousMessages.length > 0) {
                const interruptedMessages = previousMessages.map(msg => `${msg.character}: ${msg.text}`).join('\n');
                contextParts.push(`Messages being processed when interrupted:\n${interruptedMessages}`);
            }

            if (contextParts.length > 0) {
                fullPrompt = `${contextParts.join('\n\n')}\n\nUser (interrupting): ${userMessage}`;
            }
        } else {
            // If no conversation ID, just use the previous messages
            const contextMessages = previousMessages.map(msg => `${msg.character}: ${msg.text}`).join('\n');
            if (contextMessages.length > 0) {
                fullPrompt = `Previous conversation:\n${contextMessages}\n\nUser (interrupting): ${userMessage}`;
            }
        }

        await ConversationService.addMessage("user", userMessage, conversation.id);

        // Get message count for logging context
        const messageCount = await ConversationWorkflowService.getMessageCount(conversation.id);

        const systemPrompt = await PromptService.getSystemPrompt();

        const llmService = ConversationWorkflowService.getLLMService();
        const llmResponse = await llmService.generate(systemPrompt, fullPrompt);

        // Log parsed LLM response with context
        logger.info(`=== LLM RESPONSE (INTERRUPTED CHAT) ===`);
        logger.info(`Conversation ID: ${conversation.id}, Message Count: ${messageCount}, Responder: default agent (interrupted)`);
        logger.info(`Previous Messages Count: ${previousMessages.length}`);
        logger.info(`Parsed Response: ${JSON.stringify(llmResponse)}`);

        if (!llmResponse || !llmResponse.reply || !Array.isArray(llmResponse.reply)) {
            logger.error(`Invalid LLM response structure: ${JSON.stringify(llmResponse)}`);
            throw new Error("Invalid response from LLM - missing or invalid reply array");
        }

        // Store original theme before processing (for character thoughts check)
        const originalTheme = llmResponse.theme;

        // Handle case where LLM determines request is not related to interpersonal skills
        if (llmResponse.reply.length === 0) {
            logger.info(`LLM determined interrupted request is not related to interpersonal skills. Theme: ${llmResponse.theme}`);

            // Don't show warning for general greetings
            if (llmResponse.theme !== "general greeting") {
                // Create a helpful response explaining this
                const helpfulResponse = {
                    character: "Fora",
                    text: "Hey! 👋 I noticed your message might not be about interpersonal workplace skills. I'm here to help with things like communication, teamwork, conflict resolution, and other people skills at work. Could you rephrase your question to focus on the interpersonal aspect? 😊",
                    delay: 1000
                };

                llmResponse.reply = [helpfulResponse];
                llmResponse.theme = llmResponse.theme || "Request clarification needed";
                llmResponse.skills = llmResponse.skills || [];
            } else {
                // For general greetings, pick one character at random and prompt immediately
                const allCharacters = ['Fora', 'Jan', 'Lou'];
                const randomCharacter = allCharacters[Math.floor(Math.random() * allCharacters.length)];

                logger.info(`General greeting detected in interrupted chat - randomly selected ${randomCharacter} to respond immediately`);

                // Generate immediate response from the selected character
                const greetingResponse = await ConversationWorkflowService.generateImmediateGreetingResponse(
                    randomCharacter,
                    userMessage,
                    conversation
                );

                if (greetingResponse) {
                    llmResponse.reply = [greetingResponse];
                } else {
                    // Fallback to empty reply if generation fails
                    llmResponse.reply = [];
                }

                llmResponse.skills = llmResponse.skills || [];
                // Suppress theme from being sent to client for general greetings
                llmResponse.theme = "";
            }
        }

        // Store all response messages, but check for duplicates in the queue first
        for (const responseMessage of llmResponse.reply) {
            // Check if this message is already being processed in the queue to prevent duplicates
            const similarityCheck = await MessageQueueService.checkSimilarity(
                conversation.id,
                responseMessage.character,
                responseMessage.text
            );

            if (!similarityCheck.isDuplicate && !similarityCheck.isSimilar) {
                await ConversationService.addMessage(responseMessage.character, responseMessage.text, conversation.id);
                logger.info(`Added interrupted response message from ${responseMessage.character}: ${responseMessage.text.substring(0, 50)}...`);
            } else {
                logger.info(`Skipped duplicate interrupted response from ${responseMessage.character} (already in queue): ${responseMessage.text.substring(0, 50)}...`);
            }
        }

        // Store theme and skills if provided in the LLM response
        if (llmResponse.theme || llmResponse.skills) {
            await ConversationWorkflowService.updateConversationMetadata(
                conversation.id,
                llmResponse.theme,
                llmResponse.skills
            );
        }

        // Get character thought information for the calling workflow to queue
        // Use original theme to determine if character thoughts should be generated
        const characterThoughts = ConversationWorkflowService.getCharacterThoughtInfo(conversation, fullPrompt, { ...llmResponse, theme: originalTheme });

        return { ...llmResponse, conversationId: conversation.id, characterThoughts };
    }
}
